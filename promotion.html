<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>华电匿名墙推广页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%);
            min-height: 100vh;
            width: 100%;
            max-width: 414px;
            margin: 0 auto;
            overflow-x: hidden;
            padding: 0;
        }

        .container {
            display: flex;
            width: 100%;
            min-height: 100vh;
            background: rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(5px);
        }

        .left-section {
            flex: 1;
            padding: 20px 15px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .phone-mockup {
            max-width: 160px;
            width: 100%;
            margin: 0 auto;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 16px rgba(33, 150, 243, 0.3);
            border: 2px solid rgba(255, 255, 255, 0.8);
        }

        .phone-mockup img {
            width: 100%;
            height: auto;
            display: block;
        }

        .right-section {
            flex: 1;
            padding: 20px 15px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .title {
            font-size: 18px;
            font-weight: bold;
            color: #1565C0;
            margin-bottom: 8px;
            text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
            line-height: 1.3;
        }

        .subtitle {
            font-size: 16px;
            font-weight: bold;
            color: #1976D2;
            margin-bottom: 15px;
            text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
            line-height: 1.2;
        }

        .qr-section {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            width: 100%;
        }

        .qr-code {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            overflow: hidden;
            background: white;
            border: 1px solid #E0E0E0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 4px;
        }

        .qr-code img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 4px;
        }

        .qr-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
        }

        .qr-text {
            font-size: 12px;
            font-weight: bold;
            color: #1565C0;
            text-align: center;
        }

        .features {
            display: flex;
            flex-direction: column;
            gap: 2px;
            align-items: center;
        }

        .feature-item {
            font-size: 10px;
            color: #1976D2;
            font-weight: 500;
            line-height: 1.3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="left-section">
            <div class="phone-mockup">
                <img src="treehole/public/公众号素材/IMG_0845 2.PNG" alt="华电匿名墙截图">
            </div>
        </div>

        <div class="right-section">
            <div class="title">
                在校生开发
            </div>
            <div class="subtitle">
                北航最好的匿名树洞社区
            </div>

            <div class="qr-section">
                <div class="qr-code">
                    <img src="treehole/public/公众号素材/reme.png" alt="二维码">
                </div>
                <div class="qr-content">
                    <div class="qr-text">扫码加入社群</div>
                    <div class="features">
                        <div class="feature-item">✓ 无广告</div>
                        <div class="feature-item">✓ 无钓鱼</div>
                        <div class="feature-item">✓ 纯净体验</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
