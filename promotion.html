<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>华电匿名墙推广页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #FFB6C1 0%, #F0E68C 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            display: flex;
            max-width: 1200px;
            width: 100%;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }

        .left-section {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .phone-mockup {
            max-width: 300px;
            width: 100%;
            margin: 0 auto;
            border-radius: 25px;
            overflow: hidden;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
        }

        .phone-mockup img {
            width: 100%;
            height: auto;
            display: block;
        }

        .right-section {
            flex: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .title {
            font-size: 48px;
            font-weight: bold;
            color: #4A0E4E;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
            line-height: 1.2;
        }

        .subtitle {
            font-size: 36px;
            font-weight: bold;
            color: #4A0E4E;
            margin-bottom: 40px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .qr-section {
            background: rgba(255, 255, 255, 0.9);
            border: 3px solid #4A0E4E;
            border-radius: 15px;
            padding: 30px;
            display: flex;
            align-items: center;
            gap: 30px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .qr-code {
            width: 150px;
            height: 150px;
            border-radius: 10px;
            overflow: hidden;
        }

        .qr-code img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .qr-text {
            display: flex;
            flex-direction: column;
            gap: 10px;
            color: #4A0E4E;
        }

        .qr-text div {
            font-size: 24px;
            font-weight: bold;
            line-height: 1.3;
        }

        @media (max-width: 768px) {
            .container {
                flex-direction: column;
                margin: 10px;
            }

            .left-section, .right-section {
                padding: 20px;
            }

            .title {
                font-size: 32px;
            }

            .subtitle {
                font-size: 24px;
            }

            .qr-section {
                flex-direction: column;
                text-align: center;
                gap: 20px;
            }

            .qr-text div {
                font-size: 20px;
            }
        }

        @media (max-width: 480px) {
            .title {
                font-size: 24px;
            }

            .subtitle {
                font-size: 18px;
            }

            .qr-text div {
                font-size: 16px;
            }

            .qr-code {
                width: 120px;
                height: 120px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="left-section">
            <div class="phone-mockup">
                <img src="treehole/public/公众号素材/IMG_0845 2.PNG" alt="华电匿名墙截图">
            </div>
        </div>
        
        <div class="right-section">
            <div class="title">
                加入华电匿名墙<br>
                链接4.17万花店人
            </div>
            
            <div class="qr-section">
                <div class="qr-code">
                    <img src="treehole/public/公众号素材/reme.png" alt="二维码">
                </div>
                <div class="qr-text">
                    <div>扫描二维码</div>
                    <div>即刻加入社群</div>
                    <div>与全校师生链接</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
