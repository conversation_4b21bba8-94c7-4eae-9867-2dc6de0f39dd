<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>华电匿名墙推广页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #FFB6C1 0%, #F0E68C 100%);
            min-height: 100vh;
            width: 100%;
            max-width: 414px;
            margin: 0 auto;
            overflow-x: hidden;
            padding: 0;
        }

        .container {
            display: flex;
            width: 100%;
            min-height: 100vh;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        .left-section {
            flex: 1;
            padding: 20px 15px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .phone-mockup {
            max-width: 160px;
            width: 100%;
            margin: 0 auto;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
        }

        .phone-mockup img {
            width: 100%;
            height: auto;
            display: block;
        }

        .right-section {
            flex: 1;
            padding: 20px 15px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .title {
            font-size: 20px;
            font-weight: bold;
            color: #4A0E4E;
            margin-bottom: 15px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
            line-height: 1.3;
        }

        .qr-section {
            background: rgba(255, 255, 255, 0.95);
            border: 2px solid #4A0E4E;
            border-radius: 10px;
            padding: 15px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            width: 100%;
        }

        .qr-code {
            width: 80px;
            height: 80px;
            border-radius: 8px;
            overflow: hidden;
        }

        .qr-code img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .qr-text {
            display: flex;
            flex-direction: column;
            gap: 3px;
            color: #4A0E4E;
        }

        .qr-text div {
            font-size: 12px;
            font-weight: bold;
            line-height: 1.2;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="left-section">
            <div class="phone-mockup">
                <img src="treehole/public/公众号素材/IMG_0845 2.PNG" alt="华电匿名墙截图">
            </div>
        </div>

        <div class="right-section">
            <div class="title">
                加入华电匿名墙<br>
                链接4.17万花店人
            </div>

            <div class="qr-section">
                <div class="qr-code">
                    <img src="treehole/public/公众号素材/reme.png" alt="二维码">
                </div>
                <div class="qr-text">
                    <div>扫描二维码</div>
                    <div>即刻加入社群</div>
                    <div>与全校师生链接</div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
