<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>华电匿名墙推广页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #FFB6C1 0%, #F0E68C 100%);
            min-height: 100vh;
            width: 100%;
            max-width: 414px;
            margin: 0 auto;
            overflow-x: hidden;
        }

        .container {
            display: flex;
            flex-direction: column;
            width: 100%;
            min-height: 100vh;
            padding: 20px;
        }

        .title-section {
            text-align: center;
            margin-bottom: 30px;
            padding-top: 20px;
        }

        .title {
            font-size: 28px;
            font-weight: bold;
            color: #4A0E4E;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
            line-height: 1.3;
        }

        .subtitle {
            font-size: 24px;
            font-weight: bold;
            color: #4A0E4E;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
        }

        .phone-section {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
        }

        .phone-mockup {
            max-width: 280px;
            width: 100%;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.3);
        }

        .phone-mockup img {
            width: 100%;
            height: auto;
            display: block;
        }

        .qr-section {
            background: rgba(255, 255, 255, 0.95);
            border: 3px solid #4A0E4E;
            border-radius: 15px;
            padding: 25px;
            margin: 0 10px;
            text-align: center;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .qr-code {
            width: 180px;
            height: 180px;
            margin: 0 auto 20px;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .qr-code img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .qr-text {
            color: #4A0E4E;
        }

        .qr-text div {
            font-size: 20px;
            font-weight: bold;
            line-height: 1.4;
            margin-bottom: 8px;
        }

        .qr-text div:last-child {
            margin-bottom: 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title-section">
            <div class="title">
                加入华电匿名墙
            </div>
            <div class="subtitle">
                链接4.17万花店人
            </div>
        </div>

        <div class="phone-section">
            <div class="phone-mockup">
                <img src="treehole/public/公众号素材/IMG_0845 2.PNG" alt="华电匿名墙截图">
            </div>
        </div>

        <div class="qr-section">
            <div class="qr-code">
                <img src="treehole/public/公众号素材/reme.png" alt="二维码">
            </div>
            <div class="qr-text">
                <div>扫描二维码</div>
                <div>即刻加入社群</div>
                <div>与全校师生链接</div>
            </div>
        </div>
    </div>
</body>
</html>
